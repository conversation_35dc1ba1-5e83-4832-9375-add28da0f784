# NuxtEvent - Professional Event Management Platform

NuxtEvent is a professional event management platform built with Nuxt.js. It provides a robust and scalable solution for managing events, including features like user authentication, event creation, and more.

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Features

- **User Authentication:** Secure login and registration functionality.
- **Event Management:** Create, view, update, and delete events.
- **API Integration:** Seamlessly connects to backend services.
- **Responsive Design:** Fully responsive and mobile-friendly user interface.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.
