import { create, type ApisauceInstance, type ApiResponse } from "apisauce";

// Global variable to store the API client instance
let _apiClient: ApisauceInstance | null = null;

/**
 * Creates and configures the main API client using apisauce
 * The base URL is read from environment variables via NUXT runtime config
 * This function must be called within a NUXT context (composable, plugin, etc.)
 */
export function createApiClient(): ApisauceInstance {
  const config = useRuntimeConfig();

  // Create the apisauce instance with base configuration
  const api = create({
    baseURL: config.public.apiBaseUrl,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    timeout: 10000, // 10 seconds timeout
  });

  // Request interceptor - runs before every request
  api.addRequestTransform((request) => {
    // Add timestamp to requests for debugging
    if (import.meta.dev) {
      console.log(
        `🚀 API Request: ${request.method?.toUpperCase()} ${request.url}`,
      );
    }

    // You can add authentication headers here if needed
    // request.headers['Authorization'] = `Bearer ${token}`
  });

  // Response interceptor - runs after every response
  api.addResponseTransform((response) => {
    if (import.meta.dev) {
      console.log(
        `📡 API Response: ${response?.status} ${response.config?.url}`,
      );
    }

    // Handle common error cases
    if (!response.ok) {
      console.error("API Error:", {
        status: response?.status,
        problem: response.problem,
        data: response.data,
        url: response.config?.url,
      });
    }
  });

  return api;
}

/**
 * Get or create the API client instance
 * This ensures we only create one instance and reuse it
 */
export function getApiClient(): ApisauceInstance {
  if (!_apiClient) {
    _apiClient = createApiClient();
  }
  return _apiClient;
}

/**
 * Common API error handler
 * Use this to handle API errors consistently across the application
 */
export function handleApiError(response: ApiResponse<any>) {
  if (!response.ok) {
    const errorMessage =
      response.data?.message ||
      response.problem ||
      "An unexpected error occurred";

    throw new Error(errorMessage);
  }

  return response.data;
}

/**
 * Helper function to make GET requests with error handling
 */
export async function apiGet<T>(url: string, params?: object): Promise<T> {
  const client = getApiClient();
  const response = await client.get<T>(url, params);
  return handleApiError(response);
}

/**
 * Helper function to make POST requests with error handling
 */
export async function apiPost<T>(url: string, data?: any): Promise<T> {
  const client = getApiClient();
  const response = await client.post<T>(url, data);
  return handleApiError(response);
}

/**
 * Helper function to make PUT requests with error handling
 */
export async function apiPut<T>(url: string, data?: any): Promise<T> {
  const client = getApiClient();
  const response = await client.put<T>(url, data);
  return handleApiError(response);
}

/**
 * Helper function to make DELETE requests with error handling
 */
export async function apiDelete<T>(url: string): Promise<T> {
  const client = getApiClient();
  const response = await client.delete<T>(url);
  return handleApiError(response);
}
