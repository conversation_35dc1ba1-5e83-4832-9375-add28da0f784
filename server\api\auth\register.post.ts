import type { RegisterRequest, RegisterResponse } from "~/types/auth";

export default defineEventHandler(async (event) => {
  try {
    // Parse the request body
    const body: RegisterRequest = await readBody(event);

    // Validate required fields
    if (!body.username || !body.fullname || !body.email || !body.password) {
      throw createError({
        statusCode: 400,
        statusMessage: "Missing required fields",
      });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      throw createError({
        statusCode: 400,
        statusMessage: "Invalid email format",
      });
    }

    // Username validation
    if (body.username.length < 3) {
      throw createError({
        statusCode: 400,
        statusMessage: "Username must be at least 3 characters",
      });
    }

    // Password validation
    if (body.password.length < 6) {
      throw createError({
        statusCode: 400,
        statusMessage: "Password must be at least 6 characters",
      });
    }

    // TODO: Add your actual registration logic here
    // For now, we'll simulate a successful registration
    
    // Check if user already exists (mock check)
    if (body.email === "<EMAIL>") {
      throw createError({
        statusCode: 409,
        statusMessage: "User with this email already exists",
      });
    }

    // Simulate user creation
    const newUser = {
      id: crypto.randomUUID(),
      username: body.username,
      fullname: body.fullname,
      email: body.email,
      createdAt: new Date().toISOString(),
    };

    // Return success response
    const response: RegisterResponse = {
      success: true,
      message: "User registered successfully",
      data: newUser,
    };

    return response;
  } catch (error: any) {
    // Handle errors
    if (error.statusCode) {
      // Re-throw HTTP errors
      throw error;
    }

    // Handle unexpected errors
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error",
    });
  }
});
