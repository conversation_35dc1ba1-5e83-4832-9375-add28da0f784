import type {
  LoginFormData,
  LoginRequest,
  LoginResponse,
  RegisterFormData,
  RegisterRequest,
  RegisterResponse,
} from "~/types/auth";

export const useAuth = () => {
  const { http } = useApi();

  const doRegister = async (
    formData: RegisterFormData,
  ): Promise<RegisterResponse> => {
    try {
      // Prepare API request data (exclude confirmPassword)
      const requestData: RegisterRequest = {
        username: formData.username.trim(),
        fullname: formData.fullname.trim(),
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
      };

      // Make API call
      const response = await http.post<RegisterResponse>(
        "/auth/register",
        requestData,
      );

      return response;
    } catch (error: any) {
      // Re-throw the error to be handled by the calling component
      // The error now includes proper response object from ApiError
      throw error;
    }
  };

  const doLogin = async (
    formData: LoginFormData,
  ): Promise<LoginResponse | null> => {
    try {
      // Prepare API request data
      const requestData: LoginRequest = {
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
      };

      // Make API call
      const response = await http.post<LoginResponse>(
        "/auth/login",
        requestData,
      );

      return response;
    } catch (error: any) {
      // Re-throw the error to be handled by the calling component
      // The error now includes proper response object from ApiError
      throw error;
    }
  };

  return {
    doRegister,
    doLogin,
  };
};
