<script lang="ts" setup>
import type { FormSubmitEvent } from "@nuxt/ui";
import { registerSchema } from "~/types/auth";
import type { RegisterFormData } from "~/types/auth";

definePageMeta({
  layout: "auth",
});

useHead({
  title: "Register",
  meta: [
    {
      name: "description",
      content: "Create a new account",
    },
  ],
});

const { doRegister } = useAuth();
const toast = useToast();

// Form state
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const formState = reactive<RegisterFormData>({
  username: "",
  fullname: "",
  email: "",
  password: "",
  confirmPassword: "",
});

// Handle form submission
const onSubmit = async (event: FormSubmitEvent<RegisterFormData>) => {
  try {
    const result = await doRegister(event.data);
    if (result.success) {
      toast.add({
        title: "Success",
        description: result.message,
        color: "success",
      });
    } else {
      toast.add({
        title: "Error",
        description: result.message,
        color: "error",
      });
    }
  } catch (error: any) {
    // Handle different types of errors appropriately
    let errorMessage = "An unexpected error occurred";

    if (error.message) {
      errorMessage = error.message;
    } else if (error.response?.data?.message) {
      errorMessage = error.response?.data?.message;
    } else if (error.response?.problem) {
      errorMessage = `Network error: ${error.response?.problem}`;
    }

    toast.add({
      title: "Error",
      description: errorMessage,
      color: "error",
    });
  }
};
</script>

<template>
  <UCard class="w-full max-w-sm">
    <div class="bg-brand-400 text-primary text-2xl font-bold">
      Create Account
    </div>
    <div class="mb-2 text-sm text-gray-500">
      Have an account?
      <span>
        <UButton
          color="neutral"
          variant="link"
          to="/auth/login"
          class="text-primary -ml-3 font-bold"
        >
          Login here
        </UButton>
      </span>
    </div>

    <UForm
      :schema="registerSchema"
      :state="formState"
      class="space-y-6"
      @submit="onSubmit"
    >
      <UFormField name="fullname">
        <UInput v-model="formState.fullname" placeholder="Fullname" />
      </UFormField>
      <UFormField name="username">
        <UInput v-model="formState.username" placeholder="Username" />
      </UFormField>
      <UFormField name="email">
        <UInput v-model="formState.email" placeholder="Email" />
      </UFormField>
      <UFormField name="password">
        <UInput
          v-model="formState.password"
          placeholder="Password"
          :type="showPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              :aria-pressed="showPassword"
              aria-controls="password"
              @click="showPassword = !showPassword"
            />
          </template>
        </UInput>
      </UFormField>
      <UFormField name="confirmPassword">
        <UInput
          v-model="formState.confirmPassword"
          placeholder="Password Confirmation"
          :type="showConfirmPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showConfirmPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="
                showConfirmPassword ? 'Hide password' : 'Show password'
              "
              :aria-pressed="showConfirmPassword"
              aria-controls="password"
              @click="showConfirmPassword = !showConfirmPassword"
            />
          </template>
        </UInput>
      </UFormField>
      <UButton type="submit" class="flex w-full justify-center text-white">
        Register
      </UButton>
    </UForm>
  </UCard>
</template>
