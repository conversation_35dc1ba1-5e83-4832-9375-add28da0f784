<script lang="ts" setup>
useHead({
  title: "Home - NuxtEvent",
  meta: [
    {
      name: "description",
      content: "Welcome to NuxtEvent - Professional Event Management Platform",
    },
  ],
});
</script>

<template>
  <div class="flex min-h-screen items-center justify-center bg-gray-50">
    <div class="w-full max-w-md space-y-8">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-700">Welcome to NuxtEvent</h1>
        <p class="mt-2 text-gray-600">Professional Event Management Platform</p>
      </div>

      <div class="space-y-4">
        <UButton
          to="/auth/login"
          class="flex w-full justify-center rounded-md border border-transparent px-4 py-2 text-white shadow-sm"
          size="xl"
        >
          Go to Login
        </UButton>
      </div>
    </div>
  </div>
</template>
