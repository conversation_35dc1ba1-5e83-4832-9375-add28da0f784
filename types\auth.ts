import { z } from "zod";

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export const registerSchema = z
  .object({
    username: z
      .string()
      .min(1, "Please enter your username")
      .min(3, "Username must be at least 3 characters")
      .max(50, "Username must be less than 50 characters")
      .regex(
        /^[a-zA-Z0-9_]+$/,
        "Username can only contain letters, numbers, and underscores",
      ),
    fullname: z
      .string()
      .min(1, "Please enter your fullname")
      .min(3, "Fullname must be at least 3 characters")
      .max(100, "Fullname must be less than 100 characters"),
    email: z
      .email("Please enter a valid email address")
      .min(1, "Please enter your email"),
    password: z
      .string()
      .min(1, "Please enter your password")
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const loginSchema = z.object({
  email: z
    .string()
    .min(1, "Please enter your email or username")
    .refine(
      (value) => {
        if (value.includes("@")) {
          return z.string().email().safeParse(value).success;
        }
        return true;
      },
      { message: "Invalid email or username" },
    ),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// ============================================================================
// FORM DATA TYPES
// ============================================================================

export type RegisterFormData = z.infer<typeof registerSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;

// ============================================================================
// API REQUEST TYPES
// ============================================================================

export interface RegisterRequest {
  username: string;
  fullname: string;
  email: string;
  password: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface RegisterResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    username: string;
    fullname: string;
    email: string;
    createdAt: string;
  };
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
  };
}
