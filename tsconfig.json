{
  // https://nuxt.com/docs/guide/concepts/typescript
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "allowSyntheticDefaultImports": true, // Allow default imports for modules without them
    "sourceMap": true, // Generate source maps for debugging
    "outDir": "dist", // Output directory for compiled files
    "strict": true, // Enable all strict type-checking options
    "noImplicitAny": true, // Raise error on variables with implicit 'any'
    "strictNullChecks": true, // Null and undefined must be explicitly handled
    "noUnusedLocals": true, // Report unused local variables
    "noUnusedParameters": true, // Report unused function parameters
    "noFallthroughCasesInSwitch": true, // Catch switch fallthroughs
    "skipLibCheck": true, // Skip checking `.d.ts` files (improves build speed)
    "forceConsistentCasingInFileNames": true // Enforce case sensitivity across file imports
  }
}


//